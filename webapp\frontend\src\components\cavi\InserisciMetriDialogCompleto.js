import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Grid,
  Paper,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip
} from '@mui/material';
import caviService from '../../services/caviService';
import parcoCaviService from '../../services/parcoCaviService';

/**
 * Dialog completo per inserire i metri posati di un cavo preselezionato
 * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato
 * 
 * @param {Object} props - Proprietà del componente
 * @param {boolean} props.open - Se il dialogo è aperto
 * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude
 * @param {Object} props.cavo - Cavo preselezionato
 * @param {string} props.cantiereId - ID del cantiere
 * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione
 * @param {Function} props.onError - Funzione chiamata in caso di errore
 * @param {boolean} props.loading - Indica se il salvataggio è in corso
 */
const InserisciMetriDialogCompleto = ({
  open = false,
  onClose = () => {},
  cavo = null,
  cantiereId,
  onSuccess = () => {},
  onError = () => {},
  loading = false
}) => {
  // Stati per il form
  const [formData, setFormData] = useState({
    metri_posati: '',
    id_bobina: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [formWarnings, setFormWarnings] = useState({});
  const [saving, setSaving] = useState(false);
  
  // Stati per bobine
  const [bobine, setBobine] = useState([]);
  const [bobineLoading, setBobineLoading] = useState(false);

  // Carica le bobine disponibili
  const loadBobine = useCallback(async () => {
    if (!cantiereId || !cavo) return;

    try {
      setBobineLoading(true);
      console.log('Caricamento bobine per cantiere:', cantiereId, 'e cavo:', cavo.id_cavo);

      const bobineData = await parcoCaviService.getBobine(cantiereId);
      console.log('Bobine caricate:', bobineData);

      // Filtra le bobine compatibili con il cavo
      const bobineCompatibili = bobineData.filter(bobina => {
        const tipologiaCompatibile = !cavo.tipologia || !bobina.tipologia ||
          cavo.tipologia.toLowerCase() === bobina.tipologia.toLowerCase();
        const sezioneCompatibile = !cavo.sezione || !bobina.sezione ||
          cavo.sezione.toLowerCase() === bobina.sezione.toLowerCase();

        return tipologiaCompatibile && sezioneCompatibile && bobina.metri_residui > 0;
      });

      console.log('Bobine compatibili filtrate:', bobineCompatibili);
      setBobine(bobineCompatibili || []);

    } catch (error) {
      console.error('Errore nel caricamento delle bobine:', error);
      setBobine([]);
    } finally {
      setBobineLoading(false);
    }
  }, [cantiereId, cavo]);

  // Reset quando si apre il dialogo
  useEffect(() => {
    if (open && cavo) {
      setFormData({
        metri_posati: cavo.metratura_reale?.toString() || '',
        id_bobina: ''
      });
      setFormErrors({});
      setFormWarnings({});
      setSaving(false);

      // Carica le bobine disponibili
      loadBobine();
    }
  }, [open, cavo, cantiereId, loadBobine]);

  // Gestisce i cambiamenti del form
  const handleFormChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Rimuovi errori quando l'utente inizia a digitare
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Validazione in tempo reale per metri posati
    if (name === 'metri_posati' && value && cavo) {
      const metri = parseFloat(value);
      if (!isNaN(metri) && metri > cavo.metri_teorici) {
        setFormWarnings(prev => ({
          ...prev,
          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`
        }));
      } else {
        setFormWarnings(prev => ({
          ...prev,
          metri_posati: ''
        }));
      }
    }
  };

  // Validazione del form
  const validateForm = () => {
    const errors = {};
    
    // Validazione metri posati
    if (!formData.metri_posati || formData.metri_posati.trim() === '') {
      errors.metri_posati = 'I metri posati sono obbligatori';
    } else {
      const metri = parseFloat(formData.metri_posati);
      if (isNaN(metri) || metri < 0) {
        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';
      }
    }
    
    // Validazione bobina
    if (!formData.id_bobina || formData.id_bobina === '') {
      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA
      if (bobine.length === 0 && !bobineLoading) {
        setFormData(prev => ({ ...prev, id_bobina: 'BOBINA_VUOTA' }));
      } else {
        errors.id_bobina = 'È necessario selezionare una bobina';
      }
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Gestisce il salvataggio
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch
    const metriPosati = parseFloat(formData.metri_posati);
    let idBobina = formData.id_bobina;

    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA
    if (bobine.length === 0 && !bobineLoading) {
      idBobina = 'BOBINA_VUOTA';
    }

    // Assicurati che BOBINA_VUOTA venga passato come stringa
    if (idBobina === 'BOBINA_VUOTA') {
      idBobina = 'BOBINA_VUOTA';
    }

    try {
      setSaving(true);
      
      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');
      console.log('- cantiereId:', cantiereId);
      console.log('- id_cavo:', cavo.id_cavo);
      console.log('- metri_posati:', metriPosati);
      console.log('- id_bobina:', idBobina);
      
      // Chiamata API con la funzione originale completa
      await caviService.updateMetriPosati(
        cantiereId,
        cavo.id_cavo,
        metriPosati,
        idBobina,
        true // Forza sempre a true per evitare blocchi
      );
      
      // Successo
      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;
      onSuccess(successMessage);
      
      // Chiudi il dialog
      handleClose();
      
    } catch (error) {
      console.error('Errore durante l\'aggiornamento dei metri posati:', error);
      
      // Gestione speciale per BOBINA_VUOTA
      if (idBobina === 'BOBINA_VUOTA' && error.success) {
        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;
        onSuccess(successMessage);
        handleClose();
        return;
      }
      
      // Gestione errori
      let errorMessage = 'Errore durante l\'aggiornamento dei metri posati';
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      onError(errorMessage);
      
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (!saving && !loading) {
      setFormErrors({});
      setFormWarnings({});
      setFormData({ metri_posati: '', id_bobina: '' });
      onClose();
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {
      handleSave();
    }
  };

  if (!cavo) return null;

  return (
    <>
      <Dialog 
        open={open} 
        onClose={handleClose} 
        maxWidth="md" 
        fullWidth
        disableEscapeKeyDown={saving || loading}
      >
        <DialogTitle>
          Inserisci Metri Posati - {cavo.id_cavo}
        </DialogTitle>
        
        <DialogContent dividers>
          {/* Informazioni cavo */}
          <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2"><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Sezione:</strong> {cavo.sezione || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2"><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Attualmente posati:</strong> {cavo.metratura_reale || 0} m</Typography>
              </Grid>
            </Grid>
          </Paper>

          {/* Campo metri posati */}
          <Box sx={{ mb: 3 }}>
            <TextField
              autoFocus
              label="Metri Posati"
              type="number"
              fullWidth
              name="metri_posati"
              value={formData.metri_posati}
              onChange={handleFormChange}
              onKeyPress={handleKeyPress}
              error={Boolean(formErrors.metri_posati)}
              helperText={formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`}
              FormHelperTextProps={{
                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }
              }}
              disabled={saving || loading}
              InputProps={{
                endAdornment: <Typography variant="body2" color="text.secondary">m</Typography>
              }}
              inputProps={{
                max: 999999,
                step: 0.1
              }}
              sx={{ mb: 2 }}
            />
          </Box>

          {/* Selezione bobina */}
          <Box sx={{ mb: 3 }}>
            <FormControl fullWidth error={Boolean(formErrors.id_bobina)}>
              <InputLabel>Bobina</InputLabel>
              <Select
                name="id_bobina"
                value={formData.id_bobina}
                onChange={handleFormChange}
                label="Bobina"
                disabled={saving || loading || bobineLoading}
              >
                {/* Opzione BOBINA_VUOTA sempre disponibile */}
                <MenuItem value="BOBINA_VUOTA">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip label="VUOTA" size="small" color="warning" variant="outlined" />
                    <Typography>BOBINA VUOTA (Posa senza bobina specifica)</Typography>
                  </Box>
                </MenuItem>

                {/* Bobine disponibili */}
                {bobine.map((bobina) => (
                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={bobina.metri_residui ? `${bobina.metri_residui.toFixed(1)}m` : '0m'}
                        size="small"
                        color={bobina.metri_residui > 0 ? 'success' : 'error'}
                        variant="outlined"
                      />
                      <Typography>
                        {bobina.id_bobina} - {bobina.tipologia || 'N/A'} {bobina.sezione || 'N/A'}
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
              {formErrors.id_bobina && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                  {formErrors.id_bobina}
                </Typography>
              )}
            </FormControl>

            {bobineLoading && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                <CircularProgress size={16} />
                <Typography variant="caption" color="text.secondary">
                  Caricamento bobine...
                </Typography>
              </Box>
            )}

            {!bobineLoading && bobine.length === 0 && (
              <Alert severity="info" sx={{ mt: 1 }}>
                Nessuna bobina compatibile disponibile. Verrà utilizzata BOBINA_VUOTA.
              </Alert>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={saving || loading}
          >
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            disabled={saving || loading || !formData.metri_posati || !formData.id_bobina}
            startIcon={saving ? <CircularProgress size={20} /> : null}
          >
            {saving ? 'Salvando...' : 'Salva'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default InserisciMetriDialogCompleto;
