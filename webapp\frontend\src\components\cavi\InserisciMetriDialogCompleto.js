import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Grid,
  Paper,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  InputAdornment,
  IconButton,
  List,
  ListItem,
  ListItemButton
} from '@mui/material';
import {
  Search as SearchIcon,
  Cancel as CancelIcon,
  AddCircleOutline as AddCircleOutlineIcon
} from '@mui/icons-material';
import caviService from '../../services/caviService';
import parcoCaviService from '../../services/parcoCaviService';
import { getReelStateColor } from '../../utils/stateUtils';

/**
 * Dialog completo per inserire i metri posati di un cavo preselezionato
 * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato
 * 
 * @param {Object} props - Proprietà del componente
 * @param {boolean} props.open - Se il dialogo è aperto
 * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude
 * @param {Object} props.cavo - Cavo preselezionato
 * @param {string} props.cantiereId - ID del cantiere
 * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione
 * @param {Function} props.onError - Funzione chiamata in caso di errore
 * @param {boolean} props.loading - Indica se il salvataggio è in corso
 */
const InserisciMetriDialogCompleto = ({
  open = false,
  onClose = () => {},
  cavo = null,
  cantiereId,
  onSuccess = () => {},
  onError = () => {},
  loading = false
}) => {
  // Stati per il form
  const [formData, setFormData] = useState({
    metri_posati: '',
    id_bobina: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [formWarnings, setFormWarnings] = useState({});
  const [saving, setSaving] = useState(false);
  
  // Stati per bobine
  const [bobine, setBobine] = useState([]);
  const [bobineLoading, setBobineLoading] = useState(false);

  // Stati per la ricerca delle bobine
  const [searchText, setSearchText] = useState('');
  const [searchType, setSearchType] = useState('contains');

  // Funzione per estrarre il numero della bobina dall'ID completo
  const getBobinaNumber = (idBobina) => {
    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}
    if (idBobina && idBobina.includes('_B')) {
      return idBobina.split('_B')[1];
    }
    return idBobina;
  };

  // Gestisce la selezione di una bobina
  const handleSelectBobina = (idBobina) => {
    console.log('Bobina selezionata:', idBobina);
    setFormData(prev => ({
      ...prev,
      id_bobina: idBobina
    }));

    // Reset degli errori
    setFormErrors(prev => ({
      ...prev,
      id_bobina: ''
    }));

    // Forza il re-render per mostrare le informazioni della bobina selezionata
    const selectedBobina = bobine.find(b => b.id_bobina === idBobina);
    if (selectedBobina) {
      console.log('Dettagli bobina selezionata:', selectedBobina);
    }
  };

  // Gestisce il cambio del testo di ricerca
  const handleSearchTextChange = (event) => {
    setSearchText(event.target.value);
  };

  // Carica le bobine disponibili
  const loadBobine = useCallback(async () => {
    if (!cantiereId || !cavo) return;

    try {
      setBobineLoading(true);
      console.log('Caricamento bobine iniziato...');

      // Carica tutte le bobine disponibili
      const bobineData = await parcoCaviService.getBobine(cantiereId);
      console.log(`Bobine caricate: ${bobineData.length}`);

      // Filtra solo per stato (disponibile o in uso) e metri residui > 0
      const bobineUtilizzabili = bobineData.filter(bobina =>
        bobina.stato_bobina !== 'Terminata' &&
        bobina.stato_bobina !== 'Over' &&
        bobina.metri_residui > 0
      );

      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);

      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte
      if (cavo && cavo.tipologia && cavo.sezione) {
        console.log('Cavo selezionato, evidenziando bobine compatibili...');
        console.log('Dati cavo:', {
          id_cavo: cavo.id_cavo,
          tipologia: cavo.tipologia,
          sezione: cavo.sezione
        });

        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui
        const cavoTipologia = String(cavo.tipologia || '').trim().toLowerCase();
        const cavoSezione = String(cavo.sezione || '0').trim();

        // Identifica le bobine compatibili
        const bobineCompatibili = [];
        const bobineNonCompatibili = [];

        // Dividi le bobine in compatibili e non compatibili
        bobineUtilizzabili.forEach(bobina => {
          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();
          const bobinaSezione = String(bobina.sezione || '0').trim();

          // Verifica compatibilità
          const tipologiaMatch = bobinaTipologia === cavoTipologia;
          const sezioneMatch = bobinaSezione === cavoSezione;
          const isCompatible = tipologiaMatch && sezioneMatch;

          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {
            'Tipologia bobina': `"${bobina.tipologia}"`,
            'Tipologia cavo': `"${cavo.tipologia}"`,
            'Tipologie uguali?': tipologiaMatch,
            'Sezione bobina': `"${String(bobina.sezione)}"`,
            'Sezione cavo': `"${String(cavo.sezione)}"`,
            'Sezioni uguali?': sezioneMatch,
            'Stato bobina': bobina.stato_bobina,
            'Metri residui': bobina.metri_residui,
            'Compatibile?': isCompatible
          });

          if (isCompatible) {
            bobineCompatibili.push(bobina);
          } else {
            bobineNonCompatibili.push(bobina);
          }
        });

        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);
        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);

        // Log dettagliato delle bobine compatibili
        if (bobineCompatibili.length > 0) {
          console.log('Dettaglio bobine compatibili:');
          bobineCompatibili.forEach(bobina => {
            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);
          });
        } else {
          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');
        }

        // Ordina entrambi gli array per metri residui (decrescente)
        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);
        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);

        // Concatena gli array: prima le compatibili, poi le non compatibili
        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];

        // Imposta le bobine nel componente
        setBobine(bobineOrdinate);
      } else {
        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui
        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');
        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);
        setBobine(bobineUtilizzabili);
      }
    } catch (error) {
      console.error('Errore nel caricamento delle bobine:', error);
      setBobine([]);
    } finally {
      setBobineLoading(false);
    }
  }, [cantiereId, cavo]);

  // Reset quando si apre il dialogo
  useEffect(() => {
    if (open && cavo) {
      setFormData({
        metri_posati: cavo.metratura_reale?.toString() || '',
        id_bobina: ''
      });
      setFormErrors({});
      setFormWarnings({});
      setSaving(false);

      // Carica le bobine disponibili
      loadBobine();
    }
  }, [open, cavo, cantiereId, loadBobine]);

  // Gestisce i cambiamenti del form
  const handleFormChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Rimuovi errori quando l'utente inizia a digitare
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Validazione in tempo reale per metri posati
    if (name === 'metri_posati' && value && cavo) {
      const metri = parseFloat(value);
      if (!isNaN(metri) && metri > cavo.metri_teorici) {
        setFormWarnings(prev => ({
          ...prev,
          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`
        }));
      } else {
        setFormWarnings(prev => ({
          ...prev,
          metri_posati: ''
        }));
      }
    }
  };

  // Validazione del form
  const validateForm = () => {
    const errors = {};
    
    // Validazione metri posati
    if (!formData.metri_posati || formData.metri_posati.trim() === '') {
      errors.metri_posati = 'I metri posati sono obbligatori';
    } else {
      const metri = parseFloat(formData.metri_posati);
      if (isNaN(metri) || metri < 0) {
        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';
      }
    }
    
    // Validazione bobina
    if (!formData.id_bobina || formData.id_bobina === '') {
      errors.id_bobina = 'È necessario selezionare una bobina o utilizzare BOBINA VUOTA';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Gestisce il salvataggio
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch
    const metriPosati = parseFloat(formData.metri_posati);
    let idBobina = formData.id_bobina;

    try {
      setSaving(true);
      
      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');
      console.log('- cantiereId:', cantiereId);
      console.log('- id_cavo:', cavo.id_cavo);
      console.log('- metri_posati:', metriPosati);
      console.log('- id_bobina:', idBobina);
      
      // Chiamata API con la funzione originale completa
      await caviService.updateMetriPosati(
        cantiereId,
        cavo.id_cavo,
        metriPosati,
        idBobina,
        true // Forza sempre a true per evitare blocchi
      );
      
      // Successo
      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;
      onSuccess(successMessage);
      
      // Chiudi il dialog
      handleClose();
      
    } catch (error) {
      console.error('Errore durante l\'aggiornamento dei metri posati:', error);
      
      // Gestione speciale per BOBINA_VUOTA
      if (idBobina === 'BOBINA_VUOTA' && error.success) {
        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;
        onSuccess(successMessage);
        handleClose();
        return;
      }
      
      // Gestione errori
      let errorMessage = 'Errore durante l\'aggiornamento dei metri posati';
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      onError(errorMessage);
      
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (!saving && !loading) {
      setFormErrors({});
      setFormWarnings({});
      setFormData({ metri_posati: '', id_bobina: '' });
      onClose();
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {
      handleSave();
    }
  };

  if (!cavo) return null;

  // Filtra le bobine in base al testo di ricerca (logica identica all'originale)
  const bobineFiltrate = bobine.filter(bobina => {
    // Se non c'è testo di ricerca, mostra tutte le bobine
    if (!searchText) return true;

    // Per la ricerca esatta, prima verifica se l'intero testo corrisponde esattamente
    if (searchType === 'equals' && !searchText.includes(',')) {
      const searchLower = searchText.trim().toLowerCase();
      const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();
      const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();
      const bobinaSezione = String(bobina.sezione || '').toLowerCase();

      // Verifica corrispondenza esatta con l'intero testo di ricerca
      return bobinaId === searchLower ||
             bobinaTipologia === searchLower ||
             bobinaSezione === searchLower;
    }

    // Gestione ricerca con virgole (termini multipli)
    const searchTerms = searchText.split(',').map(term => term.trim().toLowerCase()).filter(term => term.length > 0);

    if (searchTerms.length > 1) {
      // Ricerca con termini multipli
      return searchTerms.some(term => {
        const termStr = String(term);
        const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();
        const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();
        const bobinaSezione = String(bobina.sezione || '').toLowerCase();

        // Verifica se il termine è un numero
        const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));

        if (isNumericTerm) {
          const numericTerm = parseFloat(termStr);
          const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));

          // Verifica corrispondenza esatta per numeri e ID
          return bobinaSezioneNum === numericTerm ||
                 bobinaId === termStr ||
                 bobinaTipologia === term ||
                 bobinaSezione === term;
        } else {
          // Ricerca esatta per stringhe
          return bobinaId === term ||
                 bobinaTipologia === term ||
                 bobinaSezione === term;
        }
      });
    }

    // Ricerca con singolo termine
    const term = searchTerms[0] || searchText.trim().toLowerCase();
    const termStr = String(term);
    const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();
    const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();
    const bobinaSezione = String(bobina.sezione || '').toLowerCase();

    // Verifica se il termine è un numero
    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));

    if (searchType === 'equals') {
      // Ricerca esatta
      if (isNumericTerm) {
        const numericTerm = parseFloat(termStr);
        const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));

        // Verifica corrispondenza esatta per numeri e ID
        return bobinaSezioneNum === numericTerm ||
               bobinaId === termStr ||
               bobinaTipologia === term ||
               bobinaSezione === term;
      } else {
        // Ricerca esatta per stringhe
        return bobinaId === term ||
               bobinaTipologia === term ||
               bobinaSezione === term;
      }
    } else {
      // Ricerca con 'contains' (comportamento predefinito)
      if (isNumericTerm) {
        const numericTerm = parseFloat(termStr);
        const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));

        // Verifica corrispondenza esatta per numeri
        if (bobinaSezioneNum === numericTerm) {
          return true;
        }

        // Verifica se il numero è contenuto nell'ID
        if (bobinaId.includes(termStr)) {
          return true;
        }
      }

      // Ricerca standard con includes
      return bobinaId.includes(term) ||
             bobinaTipologia.includes(term) ||
             bobinaSezione.includes(term);
    }
  });

  // Separa le bobine compatibili e non compatibili
  const bobineCompatibili = cavo
    ? bobineFiltrate.filter(bobina =>
        String(bobina.tipologia || '').trim() === String(cavo.tipologia || '').trim() &&
        String(bobina.sezione || '0').trim() === String(cavo.sezione || '0').trim())
    : bobineFiltrate;

  const bobineNonCompatibili = cavo
    ? bobineFiltrate.filter(bobina =>
        String(bobina.tipologia || '').trim() !== String(cavo.tipologia || '').trim() ||
        String(bobina.sezione || '0').trim() !== String(cavo.sezione || '0').trim())
    : [];

  return (
    <>
      <Dialog 
        open={open} 
        onClose={handleClose} 
        maxWidth="md" 
        fullWidth
        disableEscapeKeyDown={saving || loading}
      >
        <DialogTitle>
          Inserisci Metri Posati - {cavo.id_cavo}
        </DialogTitle>
        
        <DialogContent dividers>
          {/* Informazioni cavo */}
          <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2"><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Sezione:</strong> {cavo.sezione || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2"><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Attualmente posati:</strong> {cavo.metratura_reale || 0} m</Typography>
              </Grid>
            </Grid>
          </Paper>

          {/* Campo metri posati */}
          <Box sx={{ mb: 3 }}>
            <TextField
              autoFocus
              label="Metri Posati"
              type="number"
              fullWidth
              name="metri_posati"
              value={formData.metri_posati}
              onChange={handleFormChange}
              onKeyPress={handleKeyPress}
              error={Boolean(formErrors.metri_posati)}
              helperText={formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`}
              FormHelperTextProps={{
                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }
              }}
              disabled={saving || loading}
              InputProps={{
                endAdornment: <Typography variant="body2" color="text.secondary">m</Typography>
              }}
              inputProps={{
                max: 999999,
                step: 0.1
              }}
              sx={{ mb: 2 }}
            />
          </Box>

          {/* Selezione bobina con doppia lista */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
              Selezione Bobina
            </Typography>

            {/* Controlli di ricerca e BOBINA_VUOTA */}
            <Box sx={{ display: 'flex', gap: 2, mb: 2, alignItems: 'center', flexWrap: 'wrap' }}>
              {/* Campo di ricerca */}
              <TextField
                size="small"
                label="Cerca"
                variant="outlined"
                value={searchText}
                onChange={handleSearchTextChange}
                placeholder="ID, tipologia, formazione..."
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                  endAdornment: searchText ? (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        aria-label="clear search"
                        onClick={() => setSearchText('')}
                        edge="end"
                      >
                        <CancelIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ) : null
                }}
                sx={{ width: '200px' }}
              />

              {/* Dropdown per il tipo di ricerca */}
              <FormControl size="small" sx={{ width: '120px' }}>
                <InputLabel id="search-type-label">Tipo ricerca</InputLabel>
                <Select
                  labelId="search-type-label"
                  value={searchType}
                  label="Tipo ricerca"
                  onChange={(e) => setSearchType(e.target.value)}
                >
                  <MenuItem value="contains">Contiene</MenuItem>
                  <MenuItem value="equals">Uguale a</MenuItem>
                </Select>
              </FormControl>

              <Button
                variant="outlined"
                size="small"
                onClick={() => handleSelectBobina('BOBINA_VUOTA')}
                sx={{
                  height: '40px',
                  fontWeight: formData.id_bobina === 'BOBINA_VUOTA' ? 'bold' : 'normal',
                  bgcolor: formData.id_bobina === 'BOBINA_VUOTA' ? 'rgba(76, 175, 80, 0.12)' : 'inherit',
                  border: formData.id_bobina === 'BOBINA_VUOTA' ? '1px solid #4caf50' : undefined
                }}
              >
                BOBINA VUOTA
              </Button>
            </Box>

            {bobineLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>
                <CircularProgress size={24} />
              </Box>
            ) : (
              <Box>
                {/* Griglia per le due liste di bobine */}
                <Grid container spacing={2}>
                  {/* Colonna sinistra: Bobine compatibili */}
                  <Grid item xs={12} md={6}>
                    <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                        ELENCO BOBINE COMPATIBILI
                      </Typography>

                      {bobineCompatibili.length > 0 ? (
                        <>
                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>
                            <Box sx={{ width: '60px', mr: 2 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>
                            </Box>
                            <Box sx={{ width: '120px', mr: 2 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>
                            </Box>
                            <Box sx={{ width: '100px', mr: 2 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>
                            </Box>
                            <Box sx={{ width: '100px', mr: 2 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>
                            </Box>
                            <Box sx={{ flexGrow: 0 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>
                            </Box>
                          </Box>
                          <List sx={{ maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>
                            {bobineCompatibili.map((bobina) => (
                              <ListItem
                                key={bobina.id_bobina}
                                disablePadding
                                secondaryAction={
                                  <IconButton
                                    edge="end"
                                    size="small"
                                    onClick={() => handleSelectBobina(bobina.id_bobina)}
                                  >
                                    <AddCircleOutlineIcon color="primary" />
                                  </IconButton>
                                }
                                sx={{
                                  bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',
                                  borderRadius: '4px',
                                  mb: 0.5,
                                  border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',
                                }}
                              >
                                <ListItemButton
                                  dense
                                  onClick={() => handleSelectBobina(bobina.id_bobina)}
                                >
                                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>
                                    <Box sx={{ width: '60px', mr: 2 }}>
                                      <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                                        {getBobinaNumber(bobina.id_bobina)}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ width: '120px', mr: 2 }}>
                                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                        {bobina.tipologia || 'N/A'}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ width: '100px', mr: 2 }}>
                                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                        {bobina.sezione || 'N/A'}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ width: '100px', mr: 2 }}>
                                      <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>
                                        {bobina.metri_residui || 0} m
                                      </Typography>
                                    </Box>
                                    <Box sx={{ flexGrow: 0 }}>
                                      <Chip
                                        size="small"
                                        label={bobina.stato_bobina || 'N/D'}
                                        color={getReelStateColor(bobina.stato_bobina)}
                                        variant="outlined"
                                        sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                                      />
                                    </Box>
                                  </Box>
                                </ListItemButton>
                              </ListItem>
                            ))}
                          </List>
                        </>
                      ) : (
                        <Alert severity="info" sx={{ mt: 1 }}>
                          Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.
                        </Alert>
                      )}
                    </Paper>
                  </Grid>

                  {/* Colonna destra: Bobine non compatibili */}
                  <Grid item xs={12} md={6}>
                    <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                        ELENCO BOBINE NON COMPATIBILI
                      </Typography>

                      {bobineNonCompatibili.length > 0 ? (
                        <>
                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>
                            <Box sx={{ width: '60px', mr: 2 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>
                            </Box>
                            <Box sx={{ width: '120px', mr: 2 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>
                            </Box>
                            <Box sx={{ width: '100px', mr: 2 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>
                            </Box>
                            <Box sx={{ width: '100px', mr: 2 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>
                            </Box>
                            <Box sx={{ flexGrow: 0 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>
                            </Box>
                          </Box>
                          <List sx={{ maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>
                            {bobineNonCompatibili.map((bobina) => (
                              <ListItem
                                key={bobina.id_bobina}
                                disablePadding
                                secondaryAction={
                                  <IconButton
                                    edge="end"
                                    size="small"
                                    onClick={() => handleSelectBobina(bobina.id_bobina)}
                                  >
                                    <AddCircleOutlineIcon color="primary" />
                                  </IconButton>
                                }
                                sx={{
                                  bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',
                                  borderRadius: '4px',
                                  mb: 0.5,
                                  border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',
                                }}
                              >
                                <ListItemButton
                                  dense
                                  onClick={() => handleSelectBobina(bobina.id_bobina)}
                                >
                                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>
                                    <Box sx={{ width: '60px', mr: 2 }}>
                                      <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                                        {getBobinaNumber(bobina.id_bobina)}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ width: '120px', mr: 2 }}>
                                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                        {bobina.tipologia || 'N/A'}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ width: '100px', mr: 2 }}>
                                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                        {bobina.sezione || 'N/A'}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ width: '100px', mr: 2 }}>
                                      <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>
                                        {bobina.metri_residui || 0} m
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Chip
                                        size="small"
                                        label={bobina.stato_bobina || 'N/D'}
                                        color={getReelStateColor(bobina.stato_bobina)}
                                        variant="outlined"
                                        sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                                      />
                                      <Chip
                                        size="small"
                                        label="Non comp."
                                        color="warning"
                                        variant="outlined"
                                        sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                                      />
                                    </Box>
                                  </Box>
                                </ListItemButton>
                              </ListItem>
                            ))}
                          </List>
                        </>
                      ) : (
                        <Alert severity="info" sx={{ mt: 1 }}>
                          Nessuna bobina non compatibile disponibile con i filtri attuali.
                        </Alert>
                      )}
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            )}

            {bobine.length === 0 && !bobineLoading && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.
              </Alert>
            )}

            {formErrors.id_bobina && (
              <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                {formErrors.id_bobina}
              </Typography>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={saving || loading}
          >
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            disabled={saving || loading || !formData.metri_posati || !formData.id_bobina}
            startIcon={saving ? <CircularProgress size={20} /> : null}
          >
            {saving ? 'Salvando...' : 'Salva'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default InserisciMetriDialogCompleto;
